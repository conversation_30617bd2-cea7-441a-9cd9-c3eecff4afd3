<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品统一开发底座技术路线</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: #f0f2f5;
        }
        .container {
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
            aspect-ratio: 16/9;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
        }
        .main-title {
            font-size: 24px;
            font-weight: bold;
            fill: white;
            text-anchor: start;
        }
        .section-title {
            font-size: 10px;
            font-weight: bold;
            fill: white;
            text-anchor: middle;
        }
        .category-title {
            font-size: 9px;
            font-weight: bold;
            fill: white;
            text-anchor: middle;
        }
        .tech-item {
            font-size: 8px;
            fill: #333;
            text-anchor: middle;
            font-weight: 500;
        }
        .tech-item-white {
            font-size: 8px;
            fill: white;
            text-anchor: middle;
            font-weight: 500;
        }
        .small-label {
            font-size: 7px;
            fill: white;
            text-anchor: middle;
        }
        
        /* Color scheme */
        .primary-red { fill: #c62828; }
        .secondary-red { fill: #d32f2f; }
        .accent-red { fill: #e53935; }
        .light-red { fill: #ef5350; }
        .dark-red { fill: #b71c1c; }
        .gray-light { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 0.5; }
        .gray-medium { fill: #e9ecef; stroke: #ced4da; stroke-width: 0.5; }
        .white-box { fill: white; stroke: #adb5bd; stroke-width: 0.5; }
        
        /* Interactive effects */
        .interactive:hover {
            opacity: 0.85;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <svg viewBox="0 0 1600 900" xmlns="http://www.w3.org/2000/svg">
            <!-- Definitions -->
            <defs>
                <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#c62828"/>
                    <stop offset="50%" style="stop-color:#d32f2f"/>
                    <stop offset="100%" style="stop-color:#c62828"/>
                </linearGradient>
                <filter id="shadow">
                    <feDropShadow dx="1" dy="1" stdDeviation="2" flood-opacity="0.3"/>
                </filter>
            </defs>
            
            <!-- Background -->
            <rect x="0" y="0" width="1600" height="900" fill="white"/>
            
            <!-- Main Header -->
            <rect x="0" y="0" width="1600" height="45" fill="url(#headerGradient)"/>
            <text x="40" y="28" class="main-title">产品统一开发底座技术路线</text>
            <text x="1200" y="18" class="small-label">开发底座支持</text>
            <text x="1350" y="18" class="small-label">具体主流</text>
            <text x="1500" y="18" class="small-label">5G</text>
            <text x="1200" y="32" class="small-label">Development Platform</text>
            <text x="1350" y="32" class="small-label">Mainstream</text>
            <text x="1500" y="32" class="small-label">5G Ready</text>
            
            <!-- Application Layer Headers -->
            <rect x="0" y="55" width="130" height="22" class="primary-red"/>
            <text x="65" y="70" class="section-title">上层应用（业务组件）</text>
            
            <rect x="0" y="82" width="130" height="22" class="secondary-red"/>
            <text x="65" y="97" class="section-title">上层应用（技术组件）</text>
            
            <!-- Business Components Row -->
            <g class="business-components">
                <rect x="140" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="182" y="70" class="category-title">多租户组件</text>
                
                <rect x="235" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="277" y="70" class="category-title">审计日志组件</text>
                
                <rect x="330" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="372" y="70" class="category-title">行为审计组件</text>
                
                <rect x="425" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="467" y="70" class="category-title">监控审计组件</text>
                
                <rect x="520" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="562" y="70" class="category-title">单人单点登录</text>
                
                <rect x="615" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="657" y="70" class="category-title">文档组件</text>
                
                <rect x="710" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="752" y="70" class="category-title">集成开发环境</text>
                
                <rect x="805" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="847" y="70" class="category-title">系统日志查看</text>
                
                <rect x="900" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="942" y="70" class="category-title">研发工具集成</text>
                
                <rect x="995" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="1037" y="70" class="category-title">配置组件</text>
                
                <rect x="1090" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="1132" y="70" class="category-title">集成组件</text>
                
                <rect x="1185" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="1227" y="70" class="category-title">web组件</text>
                
                <rect x="1280" y="55" width="85" height="22" class="accent-red interactive"/>
                <text x="1322" y="70" class="category-title">集成组件</text>
                
                <rect x="1375" y="55" width="115" height="22" class="accent-red interactive"/>
                <text x="1432" y="70" class="category-title">分布式组件</text>
                
                <rect x="1500" y="55" width="90" height="22" class="accent-red interactive"/>
                <text x="1545" y="70" class="category-title">UaaService</text>
            </g>
            
            <!-- Technical Components Row -->
            <g class="technical-components">
                <rect x="140" y="82" width="65" height="22" class="light-red interactive"/>
                <text x="172" y="97" class="category-title">4A组件</text>
                
                <rect x="215" y="82" width="65" height="22" class="light-red interactive"/>
                <text x="247" y="97" class="category-title">数据库组件</text>
                
                <rect x="290" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="332" y="97" class="category-title">文档上传下载</text>
                
                <rect x="385" y="82" width="65" height="22" class="light-red interactive"/>
                <text x="417" y="97" class="category-title">缓存组件</text>
                
                <rect x="460" y="82" width="65" height="22" class="light-red interactive"/>
                <text x="492" y="97" class="category-title">对象存储</text>
                
                <rect x="535" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="577" y="97" class="category-title">对象存储组件</text>
                
                <rect x="630" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="672" y="97" class="category-title">ScalaFx组件</text>
                
                <rect x="725" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="767" y="97" class="category-title">Plumelog</text>
                
                <rect x="820" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="862" y="97" class="category-title">文件存储</text>
                
                <rect x="915" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="957" y="97" class="category-title">系统监控</text>
                
                <rect x="1010" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="1052" y="97" class="category-title">hooks组件</text>
                
                <rect x="1105" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="1147" y="97" class="category-title">上传组件</text>
                
                <rect x="1200" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="1242" y="97" class="category-title">集成</text>
                
                <rect x="1295" y="82" width="85" height="22" class="light-red interactive"/>
                <text x="1337" y="97" class="category-title">redis组件</text>
                
                <rect x="1390" y="82" width="100" height="22" class="light-red interactive"/>
                <text x="1440" y="97" class="category-title">UaaService</text>
                
                <rect x="1500" y="82" width="90" height="22" class="light-red interactive"/>
                <text x="1545" y="97" class="category-title">分布式组件</text>
            </g>
        </svg>
    </div>
</body>
</html>
