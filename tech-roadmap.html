<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品统一开发底座技术路线</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
            aspect-ratio: 16/9;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
        }
        .header-text {
            font-size: 32px;
            font-weight: bold;
            fill: white;
        }
        .section-header {
            font-size: 12px;
            font-weight: bold;
            fill: white;
        }
        .tech-text {
            font-size: 10px;
            fill: #333;
            font-weight: 500;
        }
        .tech-text-white {
            font-size: 10px;
            fill: white;
            font-weight: 500;
        }
        .category-text {
            font-size: 11px;
            font-weight: bold;
            fill: white;
        }
        .small-text {
            font-size: 8px;
            fill: #666;
        }
        .red-bg { fill: #c62828; }
        .dark-red-bg { fill: #8e0000; }
        .light-red-bg { fill: #e53935; }
        .gray-bg { fill: #f5f5f5; stroke: #ddd; stroke-width: 1; }
        .white-bg { fill: white; stroke: #bbb; stroke-width: 1; }
        .gradient-bg { fill: url(#redGradient); }

        /* Hover effects */
        rect:hover {
            opacity: 0.9;
            cursor: pointer;
        }

        /* Responsive text */
        @media (max-width: 1200px) {
            .header-text { font-size: 24px; }
            .section-header { font-size: 10px; }
            .tech-text, .tech-text-white { font-size: 8px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <svg viewBox="0 0 1600 900" xmlns="http://www.w3.org/2000/svg">
            <!-- Gradients and Definitions -->
            <defs>
                <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#d32f2f;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#b71c1c;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#c62828;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#d32f2f;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#c62828;stop-opacity:1" />
                </linearGradient>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
                </filter>
            </defs>

            <!-- Background -->
            <rect x="0" y="0" width="1600" height="900" fill="#fafafa"/>

            <!-- Header -->
            <rect x="0" y="0" width="1600" height="60" fill="url(#headerGradient)" filter="url(#shadow)"/>
            <text x="30" y="42" class="header-text">产品统一开发底座技术路线</text>
            <text x="1200" y="25" class="tech-text-white">开发底座支持</text>
            <text x="1350" y="25" class="tech-text-white">具体主流</text>
            <text x="1500" y="25" class="tech-text-white">5G</text>
            <text x="1200" y="45" class="small-text" fill="white">开发底座支持</text>
            <text x="1350" y="45" class="small-text" fill="white">具体主流</text>
            <text x="1500" y="45" class="small-text" fill="white">5G</text>
            
            <!-- Left Column Headers -->
            <rect x="10" y="70" width="120" height="30" class="red-bg"/>
            <text x="70" y="90" class="section-header" text-anchor="middle">上层应用（业务组件）</text>
            
            <rect x="10" y="110" width="120" height="30" class="red-bg"/>
            <text x="70" y="130" class="section-header" text-anchor="middle">上层应用（技术组件）</text>
            
            <!-- Development Environment Section -->
            <rect x="10" y="150" width="60" height="120" class="red-bg"/>
            <text x="40" y="175" class="section-header" text-anchor="middle">开</text>
            <text x="40" y="195" class="section-header" text-anchor="middle">发</text>
            <text x="40" y="215" class="section-header" text-anchor="middle">环</text>
            <text x="40" y="235" class="section-header" text-anchor="middle">境</text>
            
            <!-- IDE Section -->
            <rect x="80" y="150" width="50" height="40" class="gray-bg"/>
            <text x="105" y="165" class="tech-text" text-anchor="middle">IDE</text>
            <text x="105" y="180" class="tech-text" text-anchor="middle">Visual Studio Code</text>
            
            <rect x="140" y="150" width="50" height="40" class="gray-bg"/>
            <text x="165" y="165" class="tech-text" text-anchor="middle">WebStorm</text>
            <text x="165" y="180" class="tech-text" text-anchor="middle">Atom</text>
            
            <!-- OS Section -->
            <rect x="80" y="200" width="50" height="35" class="gray-bg"/>
            <text x="105" y="215" class="tech-text" text-anchor="middle">操作</text>
            <text x="105" y="230" class="tech-text" text-anchor="middle">系统</text>
            
            <rect x="140" y="200" width="50" height="35" class="gray-bg"/>
            <text x="165" y="215" class="tech-text" text-anchor="middle">Windows</text>
            <text x="165" y="230" class="tech-text" text-anchor="middle">MacOS</text>
            
            <rect x="200" y="200" width="50" height="35" class="gray-bg"/>
            <text x="225" y="215" class="tech-text" text-anchor="middle">CentOS</text>
            <text x="225" y="230" class="tech-text" text-anchor="middle">Ubuntu</text>
            
            <!-- Programming Languages -->
            <rect x="10" y="280" width="60" height="80" class="red-bg"/>
            <text x="40" y="305" class="section-header" text-anchor="middle">开</text>
            <text x="40" y="325" class="section-header" text-anchor="middle">发</text>
            <text x="40" y="345" class="section-header" text-anchor="middle">语</text>
            <text x="40" y="365" class="section-header" text-anchor="middle">言</text>
            
            <rect x="80" y="280" width="50" height="40" class="gray-bg"/>
            <text x="105" y="295" class="tech-text" text-anchor="middle">Java 8</text>
            <text x="105" y="310" class="tech-text" text-anchor="middle">Java17</text>
            
            <rect x="140" y="280" width="110" height="40" class="gray-bg"/>
            <text x="195" y="295" class="tech-text" text-anchor="middle">SpringBoot 2.7.2</text>
            <text x="195" y="310" class="tech-text" text-anchor="middle">SpringBoot 3.0.0</text>
            
            <!-- Business Applications Top Row -->
            <rect x="260" y="70" width="80" height="30" class="red-bg"/>
            <text x="300" y="90" class="section-header" text-anchor="middle">多租户组件</text>
            
            <rect x="350" y="70" width="80" height="30" class="red-bg"/>
            <text x="390" y="90" class="section-header" text-anchor="middle">审计日志组件</text>
            
            <rect x="440" y="70" width="80" height="30" class="red-bg"/>
            <text x="480" y="90" class="section-header" text-anchor="middle">行为审计组件</text>
            
            <rect x="530" y="70" width="80" height="30" class="red-bg"/>
            <text x="570" y="90" class="section-header" text-anchor="middle">监控审计组件</text>
            
            <rect x="620" y="70" width="80" height="30" class="red-bg"/>
            <text x="660" y="90" class="section-header" text-anchor="middle">单人单点登录组件</text>
            
            <rect x="710" y="70" width="80" height="30" class="red-bg"/>
            <text x="750" y="90" class="section-header" text-anchor="middle">文档组件</text>
            
            <rect x="800" y="70" width="80" height="30" class="red-bg"/>
            <text x="840" y="90" class="section-header" text-anchor="middle">集成开发环境组件</text>
            
            <rect x="890" y="70" width="80" height="30" class="red-bg"/>
            <text x="930" y="90" class="section-header" text-anchor="middle">系统日志查看组件</text>
            
            <rect x="980" y="70" width="80" height="30" class="red-bg"/>
            <text x="1020" y="90" class="section-header" text-anchor="middle">研发工具集成组件</text>
            
            <rect x="1070" y="70" width="80" height="30" class="red-bg"/>
            <text x="1110" y="90" class="section-header" text-anchor="middle">配置组件</text>
            
            <rect x="1160" y="70" width="80" height="30" class="red-bg"/>
            <text x="1200" y="90" class="section-header" text-anchor="middle">集成组件</text>
            
            <!-- Technical Applications Row -->
            <rect x="260" y="110" width="60" height="30" class="red-bg"/>
            <text x="290" y="130" class="section-header" text-anchor="middle">4A组件</text>
            
            <rect x="330" y="110" width="60" height="30" class="red-bg"/>
            <text x="360" y="130" class="section-header" text-anchor="middle">数据库组件</text>
            
            <rect x="400" y="110" width="60" height="30" class="red-bg"/>
            <text x="430" y="130" class="section-header" text-anchor="middle">文档上传下载组件</text>
            
            <!-- Framework Section -->
            <rect x="260" y="280" width="100" height="80" class="red-bg"/>
            <text x="310" y="305" class="section-header" text-anchor="middle">应用框架支持</text>

            <!-- Framework Technologies -->
            <rect x="370" y="280" width="80" height="25" class="red-bg"/>
            <text x="410" y="297" class="tech-text-white" text-anchor="middle">Kotlin</text>

            <rect x="370" y="310" width="80" height="25" class="red-bg"/>
            <text x="410" y="327" class="tech-text-white" text-anchor="middle">Open JDK</text>

            <rect x="370" y="340" width="80" height="25" class="red-bg"/>
            <text x="410" y="357" class="tech-text-white" text-anchor="middle">Spring</text>

            <rect x="460" y="280" width="80" height="25" class="red-bg"/>
            <text x="500" y="297" class="tech-text-white" text-anchor="middle">Swagger</text>

            <rect x="460" y="310" width="80" height="25" class="red-bg"/>
            <text x="500" y="327" class="tech-text-white" text-anchor="middle">Swagger UI</text>

            <rect x="460" y="340" width="80" height="25" class="red-bg"/>
            <text x="500" y="357" class="tech-text-white" text-anchor="middle">Caffeine</text>

            <!-- Database Section -->
            <rect x="550" y="280" width="80" height="80" class="red-bg"/>
            <text x="590" y="305" class="section-header" text-anchor="middle">数据</text>
            <text x="590" y="325" class="section-header" text-anchor="middle">库</text>
            <text x="590" y="345" class="section-header" text-anchor="middle">支持</text>

            <rect x="640" y="280" width="60" height="25" class="white-bg"/>
            <text x="670" y="297" class="tech-text" text-anchor="middle">Minio</text>

            <rect x="640" y="310" width="60" height="25" class="white-bg"/>
            <text x="670" y="327" class="tech-text" text-anchor="middle">SpringCloud 2021.0.8</text>

            <rect x="640" y="340" width="60" height="25" class="white-bg"/>
            <text x="670" y="357" class="tech-text" text-anchor="middle">SpringCloud Alibaba 2021.0.5.0</text>

            <!-- Microservices Section -->
            <rect x="710" y="280" width="100" height="30" class="red-bg"/>
            <text x="760" y="300" class="section-header" text-anchor="middle">微服务架构</text>

            <rect x="710" y="320" width="100" height="40" class="white-bg"/>
            <text x="760" y="335" class="tech-text" text-anchor="middle">SpringCloud Gateway</text>
            <text x="760" y="350" class="tech-text" text-anchor="middle">网关</text>

            <!-- Service Governance -->
            <rect x="820" y="280" width="100" height="30" class="red-bg"/>
            <text x="870" y="300" class="section-header" text-anchor="middle">服务治理</text>

            <rect x="820" y="320" width="100" height="40" class="white-bg"/>
            <text x="870" y="335" class="tech-text" text-anchor="middle">Sentinel 1.8.6</text>
            <text x="870" y="350" class="tech-text" text-anchor="middle">Pak</text>

            <!-- Distributed Architecture -->
            <rect x="930" y="280" width="100" height="30" class="red-bg"/>
            <text x="980" y="300" class="section-header" text-anchor="middle">分布式架构支撑</text>

            <rect x="930" y="320" width="100" height="40" class="white-bg"/>
            <text x="980" y="335" class="tech-text" text-anchor="middle">PowerJob 4.3.0</text>
            <text x="980" y="350" class="tech-text" text-anchor="middle">XXL-Job 2.4.0</text>

            <!-- Configuration Center -->
            <rect x="1040" y="280" width="100" height="30" class="red-bg"/>
            <text x="1090" y="300" class="section-header" text-anchor="middle">配置中心</text>

            <rect x="1040" y="320" width="100" height="40" class="white-bg"/>
            <text x="1090" y="335" class="tech-text" text-anchor="middle">Plumelog 3.5.0</text>
            <text x="1090" y="350" class="tech-text" text-anchor="middle">上</text>

            <!-- Message Queue -->
            <rect x="1150" y="280" width="100" height="30" class="red-bg"/>
            <text x="1200" y="300" class="section-header" text-anchor="middle">消息中间件</text>

            <rect x="1150" y="320" width="100" height="40" class="white-bg"/>
            <text x="1200" y="335" class="tech-text" text-anchor="middle">RocketMQ 5.0</text>
            <text x="1200" y="350" class="tech-text" text-anchor="middle">RabbitMQ</text>

            <!-- Frontend Section -->
            <rect x="10" y="380" width="60" height="120" class="red-bg"/>
            <text x="40" y="405" class="section-header" text-anchor="middle">前</text>
            <text x="40" y="425" class="section-header" text-anchor="middle">端</text>
            <text x="40" y="445" class="section-header" text-anchor="middle">技</text>
            <text x="40" y="465" class="section-header" text-anchor="middle">术</text>
            <text x="40" y="485" class="section-header" text-anchor="middle">栈</text>

            <!-- Frontend Technologies -->
            <rect x="80" y="380" width="60" height="30" class="gray-bg"/>
            <text x="110" y="400" class="tech-text" text-anchor="middle">JavaScript</text>

            <rect x="150" y="380" width="60" height="30" class="gray-bg"/>
            <text x="180" y="400" class="tech-text" text-anchor="middle">TypeScript</text>

            <rect x="80" y="420" width="60" height="30" class="gray-bg"/>
            <text x="110" y="440" class="tech-text" text-anchor="middle">Python 3.8.10</text>

            <rect x="150" y="420" width="60" height="30" class="gray-bg"/>
            <text x="180" y="440" class="tech-text" text-anchor="middle">Node.js</text>

            <rect x="80" y="460" width="60" height="30" class="gray-bg"/>
            <text x="110" y="480" class="tech-text" text-anchor="middle">npm</text>

            <rect x="150" y="460" width="60" height="30" class="gray-bg"/>
            <text x="180" y="480" class="tech-text" text-anchor="middle">yarn</text>

            <!-- Build Tools -->
            <rect x="220" y="380" width="80" height="120" class="red-bg"/>
            <text x="260" y="405" class="section-header" text-anchor="middle">构</text>
            <text x="260" y="425" class="section-header" text-anchor="middle">建</text>
            <text x="260" y="445" class="section-header" text-anchor="middle">工</text>
            <text x="260" y="465" class="section-header" text-anchor="middle">具</text>

            <!-- Testing Tools -->
            <rect x="310" y="380" width="80" height="30" class="red-bg"/>
            <text x="350" y="400" class="section-header" text-anchor="middle">测试工具</text>

            <rect x="310" y="420" width="80" height="25" class="white-bg"/>
            <text x="350" y="437" class="tech-text" text-anchor="middle">JUnit</text>

            <rect x="310" y="450" width="80" height="25" class="white-bg"/>
            <text x="350" y="467" class="tech-text" text-anchor="middle">Mockito</text>

            <rect x="310" y="475" width="80" height="25" class="white-bg"/>
            <text x="350" y="492" class="tech-text" text-anchor="middle">TestNG</text>

            <!-- Monitoring Tools -->
            <rect x="400" y="380" width="80" height="30" class="red-bg"/>
            <text x="440" y="400" class="section-header" text-anchor="middle">监控工具</text>

            <rect x="400" y="420" width="80" height="25" class="white-bg"/>
            <text x="440" y="437" class="tech-text" text-anchor="middle">Skywalking</text>

            <rect x="400" y="450" width="80" height="25" class="white-bg"/>
            <text x="440" y="467" class="tech-text" text-anchor="middle">Prometheus</text>

            <rect x="400" y="475" width="80" height="25" class="white-bg"/>
            <text x="440" y="492" class="tech-text" text-anchor="middle">Grafana</text>

            <!-- Container Technology -->
            <rect x="490" y="380" width="80" height="30" class="red-bg"/>
            <text x="530" y="400" class="section-header" text-anchor="middle">容器技术</text>

            <rect x="490" y="420" width="80" height="25" class="white-bg"/>
            <text x="530" y="437" class="tech-text" text-anchor="middle">Docker</text>

            <rect x="490" y="450" width="80" height="25" class="white-bg"/>
            <text x="530" y="467" class="tech-text" text-anchor="middle">Kubernetes</text>

            <rect x="490" y="475" width="80" height="25" class="white-bg"/>
            <text x="530" y="492" class="tech-text" text-anchor="middle">Helm</text>

            <!-- DevOps Tools -->
            <rect x="580" y="380" width="80" height="30" class="red-bg"/>
            <text x="620" y="400" class="section-header" text-anchor="middle">DevOps工具</text>

            <rect x="580" y="420" width="80" height="25" class="white-bg"/>
            <text x="620" y="437" class="tech-text" text-anchor="middle">Jenkins</text>

            <rect x="580" y="450" width="80" height="25" class="white-bg"/>
            <text x="620" y="467" class="tech-text" text-anchor="middle">GitLab CI</text>

            <rect x="580" y="475" width="80" height="25" class="white-bg"/>
            <text x="620" y="492" class="tech-text" text-anchor="middle">Ansible</text>

            <!-- Database Technologies -->
            <rect x="670" y="380" width="80" height="30" class="red-bg"/>
            <text x="710" y="400" class="section-header" text-anchor="middle">数据库技术</text>

            <rect x="670" y="420" width="80" height="25" class="white-bg"/>
            <text x="710" y="437" class="tech-text" text-anchor="middle">MySQL</text>

            <rect x="670" y="450" width="80" height="25" class="white-bg"/>
            <text x="710" y="467" class="tech-text" text-anchor="middle">Redis</text>

            <rect x="670" y="475" width="80" height="25" class="white-bg"/>
            <text x="710" y="492" class="tech-text" text-anchor="middle">MongoDB</text>

            <!-- Security -->
            <rect x="760" y="380" width="80" height="30" class="red-bg"/>
            <text x="800" y="400" class="section-header" text-anchor="middle">安全组件</text>

            <rect x="760" y="420" width="80" height="25" class="white-bg"/>
            <text x="800" y="437" class="tech-text" text-anchor="middle">OAuth2</text>

            <rect x="760" y="450" width="80" height="25" class="white-bg"/>
            <text x="800" y="467" class="tech-text" text-anchor="middle">JWT</text>

            <rect x="760" y="475" width="80" height="25" class="white-bg"/>
            <text x="800" y="492" class="tech-text" text-anchor="middle">Spring Security</text>

            <!-- API Gateway -->
            <rect x="850" y="380" width="80" height="30" class="red-bg"/>
            <text x="890" y="400" class="section-header" text-anchor="middle">API网关</text>

            <rect x="850" y="420" width="80" height="25" class="white-bg"/>
            <text x="890" y="437" class="tech-text" text-anchor="middle">Zuul</text>

            <rect x="850" y="450" width="80" height="25" class="white-bg"/>
            <text x="890" y="467" class="tech-text" text-anchor="middle">Gateway</text>

            <rect x="850" y="475" width="80" height="25" class="white-bg"/>
            <text x="890" y="492" class="tech-text" text-anchor="middle">Kong</text>

            <!-- Search Engine -->
            <rect x="940" y="380" width="80" height="30" class="red-bg"/>
            <text x="980" y="400" class="section-header" text-anchor="middle">搜索引擎</text>

            <rect x="940" y="420" width="80" height="25" class="white-bg"/>
            <text x="980" y="437" class="tech-text" text-anchor="middle">Elasticsearch</text>

            <rect x="940" y="450" width="80" height="25" class="white-bg"/>
            <text x="980" y="467" class="tech-text" text-anchor="middle">Solr</text>

            <rect x="940" y="475" width="80" height="25" class="white-bg"/>
            <text x="980" y="492" class="tech-text" text-anchor="middle">Lucene</text>

            <!-- Big Data -->
            <rect x="1030" y="380" width="80" height="30" class="red-bg"/>
            <text x="1070" y="400" class="section-header" text-anchor="middle">大数据</text>

            <rect x="1030" y="420" width="80" height="25" class="white-bg"/>
            <text x="1070" y="437" class="tech-text" text-anchor="middle">Hadoop</text>

            <rect x="1030" y="450" width="80" height="25" class="white-bg"/>
            <text x="1070" y="467" class="tech-text" text-anchor="middle">Spark</text>

            <rect x="1030" y="475" width="80" height="25" class="white-bg"/>
            <text x="1070" y="492" class="tech-text" text-anchor="middle">Kafka</text>

            <!-- Cloud Native -->
            <rect x="1120" y="380" width="80" height="30" class="red-bg"/>
            <text x="1160" y="400" class="section-header" text-anchor="middle">云原生</text>

            <rect x="1120" y="420" width="80" height="25" class="white-bg"/>
            <text x="1160" y="437" class="tech-text" text-anchor="middle">Istio</text>

            <rect x="1120" y="450" width="80" height="25" class="white-bg"/>
            <text x="1160" y="467" class="tech-text" text-anchor="middle">Envoy</text>

            <rect x="1120" y="475" width="80" height="25" class="white-bg"/>
            <text x="1160" y="492" class="tech-text" text-anchor="middle">Consul</text>

            <!-- Bottom Section - Infrastructure -->
            <rect x="10" y="520" width="1580" height="40" class="dark-red-bg"/>
            <text x="800" y="545" class="section-header" text-anchor="middle" font-size="16">基础设施层</text>

            <!-- Infrastructure Components -->
            <rect x="50" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="110" y="590" class="tech-text-white" text-anchor="middle">服务器集群</text>
            <text x="110" y="610" class="tech-text-white" text-anchor="middle">负载均衡</text>

            <rect x="200" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="260" y="590" class="tech-text-white" text-anchor="middle">网络设备</text>
            <text x="260" y="610" class="tech-text-white" text-anchor="middle">防火墙</text>

            <rect x="350" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="410" y="590" class="tech-text-white" text-anchor="middle">存储系统</text>
            <text x="410" y="610" class="tech-text-white" text-anchor="middle">备份系统</text>

            <rect x="500" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="560" y="590" class="tech-text-white" text-anchor="middle">监控系统</text>
            <text x="560" y="610" class="tech-text-white" text-anchor="middle">日志系统</text>

            <rect x="650" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="710" y="590" class="tech-text-white" text-anchor="middle">CI/CD平台</text>
            <text x="710" y="610" class="tech-text-white" text-anchor="middle">自动化部署</text>

            <rect x="800" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="860" y="590" class="tech-text-white" text-anchor="middle">容器编排</text>
            <text x="860" y="610" class="tech-text-white" text-anchor="middle">服务网格</text>

            <rect x="950" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="1010" y="590" class="tech-text-white" text-anchor="middle">安全防护</text>
            <text x="1010" y="610" class="tech-text-white" text-anchor="middle">访问控制</text>

            <rect x="1100" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="1160" y="590" class="tech-text-white" text-anchor="middle">数据中心</text>
            <text x="1160" y="610" class="tech-text-white" text-anchor="middle">云平台</text>

            <rect x="1250" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="1310" y="590" class="tech-text-white" text-anchor="middle">运维工具</text>
            <text x="1310" y="610" class="tech-text-white" text-anchor="middle">性能调优</text>

            <rect x="1400" y="570" width="120" height="60" class="light-red-bg"/>
            <text x="1460" y="590" class="tech-text-white" text-anchor="middle">灾备系统</text>
            <text x="1460" y="610" class="tech-text-white" text-anchor="middle">高可用</text>

            <!-- Footer -->
            <rect x="0" y="650" width="1600" height="50" class="red-bg"/>
            <text x="800" y="680" class="section-header" text-anchor="middle" font-size="14">产品统一开发底座技术路线图 - 支撑企业级应用开发的完整技术栈</text>

        </svg>
    </div>
</body>
</html>
