<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品统一开发底座技术路线</title>
    <style>
        body {
            margin: 0;
            padding: 10px;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: #f0f2f5;
        }
        .container {
            width: 100%;
            max-width: 1600px;
            margin: 0 auto;
            aspect-ratio: 16/9;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        svg {
            width: 100%;
            height: 100%;
        }
        .main-title {
            font-size: 24px;
            font-weight: bold;
            fill: #d32f2f;
            text-anchor: start;
        }
        .section-title {
            font-size: 10px;
            font-weight: bold;
            fill: white;
            text-anchor: middle;
        }
        .category-title {
            font-size: 8px;
            font-weight: bold;
            fill: white;
            text-anchor: middle;
        }
        .tech-item {
            font-size: 7px;
            fill: #333;
            text-anchor: middle;
            font-weight: 500;
        }
        .tech-item-white {
            font-size: 7px;
            fill: white;
            text-anchor: middle;
            font-weight: 500;
        }
        .small-label {
            font-size: 8px;
            fill: #666;
            text-anchor: middle;
        }
        
        /* Color scheme matching original */
        .red-header { fill: #d32f2f; }
        .red-section { fill: #c62828; }
        .red-category { fill: #e53935; }
        .red-dark { fill: #b71c1c; }
        .gray-light { fill: #f5f5f5; stroke: #ddd; stroke-width: 0.5; }
        .gray-medium { fill: #e0e0e0; stroke: #ccc; stroke-width: 0.5; }
        .white-box { fill: white; stroke: #bbb; stroke-width: 0.5; }
        
        .interactive:hover {
            opacity: 0.9;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <svg viewBox="0 0 1600 900" xmlns="http://www.w3.org/2000/svg">
            <!-- Background -->
            <rect x="0" y="0" width="1600" height="900" fill="white"/>
            
            <!-- Main Header -->
            <rect x="0" y="0" width="1600" height="50" class="red-header"/>
            <text x="30" y="32" class="main-title" fill="white">产品统一开发底座技术路线</text>
            <text x="1200" y="20" class="small-label" fill="white">开发底座支持</text>
            <text x="1350" y="20" class="small-label" fill="white">具体主流</text>
            <text x="1500" y="20" class="small-label" fill="white">5G</text>
            <text x="1500" y="35" class="small-label" fill="white">Ready</text>
            
            <!-- Left Column - Development Environment -->
            <rect x="10" y="60" width="40" height="25" class="red-section"/>
            <text x="30" y="77" class="section-title">开发</text>
            
            <rect x="10" y="90" width="40" height="25" class="red-section"/>
            <text x="30" y="107" class="section-title">环境</text>
            
            <rect x="10" y="120" width="40" height="25" class="red-section"/>
            <text x="30" y="137" class="section-title">操作</text>
            
            <rect x="10" y="150" width="40" height="25" class="red-section"/>
            <text x="30" y="167" class="section-title">系统</text>
            
            <!-- IDE Section -->
            <rect x="60" y="60" width="50" height="25" class="gray-light interactive"/>
            <text x="85" y="77" class="tech-item">Idea</text>
            
            <rect x="120" y="60" width="50" height="25" class="gray-light interactive"/>
            <text x="145" y="77" class="tech-item">WebStorm</text>
            
            <rect x="60" y="90" width="50" height="25" class="gray-light interactive"/>
            <text x="85" y="107" class="tech-item">Visual Studio Code</text>
            
            <rect x="120" y="90" width="50" height="25" class="gray-light interactive"/>
            <text x="145" y="107" class="tech-item">Atom</text>
            
            <!-- Operating Systems -->
            <rect x="60" y="120" width="50" height="25" class="gray-medium interactive"/>
            <text x="85" y="137" class="tech-item">Windows</text>
            
            <rect x="120" y="120" width="50" height="25" class="gray-medium interactive"/>
            <text x="145" y="137" class="tech-item">MacOS</text>
            
            <rect x="60" y="150" width="50" height="25" class="gray-medium interactive"/>
            <text x="85" y="167" class="tech-item">CentOS</text>
            
            <rect x="120" y="150" width="50" height="25" class="gray-medium interactive"/>
            <text x="145" y="167" class="tech-item">Ubuntu</text>
            
            <!-- Top Application Headers -->
            <rect x="180" y="60" width="1410" height="25" class="red-category"/>
            <text x="885" y="77" class="category-title">上层应用（业务组件）</text>
            
            <rect x="180" y="90" width="1410" height="25" class="red-category"/>
            <text x="885" y="107" class="category-title">上层应用（技术组件）</text>
            
            <!-- Business Components - Row 1 -->
            <rect x="230" y="120" width="80" height="55" class="white-box interactive"/>
            <text x="270" y="135" class="tech-item">多租户组件</text>
            <text x="270" y="150" class="tech-item">审计日志组件</text>
            <text x="270" y="165" class="tech-item">行为审计组件</text>
            
            <rect x="320" y="120" width="80" height="55" class="white-box interactive"/>
            <text x="360" y="135" class="tech-item">监控审计组件</text>
            <text x="360" y="150" class="tech-item">单人单点登录</text>
            <text x="360" y="165" class="tech-item">文档组件</text>
            
            <rect x="410" y="120" width="80" height="55" class="white-box interactive"/>
            <text x="450" y="135" class="tech-item">集成开发环境</text>
            <text x="450" y="150" class="tech-item">系统日志查看</text>
            <text x="450" y="165" class="tech-item">研发工具集成</text>
            
            <rect x="500" y="120" width="80" height="55" class="white-box interactive"/>
            <text x="540" y="135" class="tech-item">配置组件</text>
            <text x="540" y="150" class="tech-item">集成组件</text>
            <text x="540" y="165" class="tech-item">web组件</text>
            
            <rect x="590" y="120" width="80" height="55" class="white-box interactive"/>
            <text x="630" y="135" class="tech-item">集成组件</text>
            <text x="630" y="150" class="tech-item">分布式组件</text>
            <text x="630" y="165" class="tech-item">UaaService</text>
            
            <!-- Technical Components - Row 2 -->
            <rect x="230" y="185" width="60" height="40" class="white-box interactive"/>
            <text x="260" y="200" class="tech-item">4A组件</text>
            <text x="260" y="215" class="tech-item">数据库组件</text>
            
            <rect x="300" y="185" width="80" height="40" class="white-box interactive"/>
            <text x="340" y="200" class="tech-item">文档上传下载</text>
            <text x="340" y="215" class="tech-item">缓存组件</text>
            
            <rect x="390" y="185" width="80" height="40" class="white-box interactive"/>
            <text x="430" y="200" class="tech-item">对象存储</text>
            <text x="430" y="215" class="tech-item">ScalaFx组件</text>
            
            <rect x="480" y="185" width="80" height="40" class="white-box interactive"/>
            <text x="520" y="200" class="tech-item">Plumelog</text>
            <text x="520" y="215" class="tech-item">文件存储</text>
            
            <rect x="570" y="185" width="80" height="40" class="white-box interactive"/>
            <text x="610" y="200" class="tech-item">系统监控</text>
            <text x="610" y="215" class="tech-item">hooks组件</text>

            <!-- Programming Languages Section -->
            <rect x="10" y="240" width="40" height="25" class="red-section"/>
            <text x="30" y="257" class="section-title">开发</text>

            <rect x="10" y="270" width="40" height="25" class="red-section"/>
            <text x="30" y="287" class="section-title">语言</text>

            <!-- Java Technologies -->
            <rect x="60" y="240" width="50" height="25" class="gray-light interactive"/>
            <text x="85" y="257" class="tech-item">Java 8</text>

            <rect x="120" y="240" width="50" height="25" class="gray-light interactive"/>
            <text x="145" y="257" class="tech-item">Java17</text>

            <rect x="60" y="270" width="80" height="25" class="gray-light interactive"/>
            <text x="100" y="287" class="tech-item">SpringBoot 2.7.2</text>

            <rect x="150" y="270" width="80" height="25" class="gray-light interactive"/>
            <text x="190" y="287" class="tech-item">SpringBoot 3.0.0</text>

            <!-- Framework Support Section -->
            <rect x="260" y="240" width="100" height="80" class="red-section"/>
            <text x="310" y="265" class="section-title">应用框架支持</text>

            <!-- Framework Technologies -->
            <rect x="370" y="240" width="80" height="25" class="red-category"/>
            <text x="410" y="257" class="tech-item-white">Kotlin</text>

            <rect x="370" y="270" width="80" height="25" class="red-category"/>
            <text x="410" y="287" class="tech-item-white">Open JDK</text>

            <rect x="370" y="300" width="80" height="25" class="red-category"/>
            <text x="410" y="317" class="tech-item-white">Spring</text>

            <rect x="460" y="240" width="80" height="25" class="red-category"/>
            <text x="500" y="257" class="tech-item-white">Swagger</text>

            <rect x="460" y="270" width="80" height="25" class="red-category"/>
            <text x="500" y="287" class="tech-item-white">Swagger UI</text>

            <rect x="460" y="300" width="80" height="25" class="red-category"/>
            <text x="500" y="317" class="tech-item-white">Caffeine</text>

            <!-- Database Support -->
            <rect x="550" y="240" width="80" height="80" class="red-section"/>
            <text x="590" y="265" class="section-title">数据</text>
            <text x="590" y="280" class="section-title">库</text>
            <text x="590" y="295" class="section-title">支持</text>

            <rect x="640" y="240" width="60" height="25" class="white-box interactive"/>
            <text x="670" y="257" class="tech-item">Minio</text>

            <rect x="640" y="270" width="100" height="25" class="white-box interactive"/>
            <text x="690" y="287" class="tech-item">SpringCloud 2021.0.8</text>

            <rect x="640" y="300" width="120" height="25" class="white-box interactive"/>
            <text x="700" y="317" class="tech-item">SpringCloud Alibaba 2021.0.5.0</text>

            <!-- Microservices Architecture -->
            <rect x="770" y="240" width="100" height="30" class="red-section"/>
            <text x="820" y="260" class="section-title">微服务架构</text>

            <rect x="770" y="280" width="100" height="40" class="white-box interactive"/>
            <text x="820" y="295" class="tech-item">SpringCloud Gateway</text>
            <text x="820" y="310" class="tech-item">网关</text>

            <!-- Service Governance -->
            <rect x="880" y="240" width="100" height="30" class="red-section"/>
            <text x="930" y="260" class="section-title">服务治理</text>

            <rect x="880" y="280" width="100" height="40" class="white-box interactive"/>
            <text x="930" y="295" class="tech-item">Sentinel 1.8.6</text>
            <text x="930" y="310" class="tech-item">Pak</text>

            <!-- Distributed Architecture -->
            <rect x="990" y="240" width="100" height="30" class="red-section"/>
            <text x="1040" y="260" class="section-title">分布式架构支撑</text>

            <rect x="990" y="280" width="100" height="40" class="white-box interactive"/>
            <text x="1040" y="295" class="tech-item">PowerJob 4.3.0</text>
            <text x="1040" y="310" class="tech-item">XXL-Job 2.4.0</text>

            <!-- Configuration Center -->
            <rect x="1100" y="240" width="100" height="30" class="red-section"/>
            <text x="1150" y="260" class="section-title">配置中心</text>

            <rect x="1100" y="280" width="100" height="40" class="white-box interactive"/>
            <text x="1150" y="295" class="tech-item">Plumelog 3.5.0</text>
            <text x="1150" y="310" class="tech-item">上</text>

            <!-- Message Queue -->
            <rect x="1210" y="240" width="100" height="30" class="red-section"/>
            <text x="1260" y="260" class="section-title">消息中间件</text>

            <rect x="1210" y="280" width="100" height="40" class="white-box interactive"/>
            <text x="1260" y="295" class="tech-item">RocketMQ 5.0</text>
            <text x="1260" y="310" class="tech-item">RabbitMQ</text>

            <!-- Frontend Technology Stack -->
            <rect x="10" y="340" width="60" height="120" class="red-section"/>
            <text x="40" y="365" class="section-title">前</text>
            <text x="40" y="385" class="section-title">端</text>
            <text x="40" y="405" class="section-title">技</text>
            <text x="40" y="425" class="section-title">术</text>
            <text x="40" y="445" class="section-title">栈</text>

            <!-- Frontend Technologies -->
            <rect x="80" y="340" width="60" height="30" class="gray-light interactive"/>
            <text x="110" y="360" class="tech-item">JavaScript</text>

            <rect x="150" y="340" width="60" height="30" class="gray-light interactive"/>
            <text x="180" y="360" class="tech-item">TypeScript</text>

            <rect x="80" y="380" width="60" height="30" class="gray-light interactive"/>
            <text x="110" y="400" class="tech-item">Python 3.8.10</text>

            <rect x="150" y="380" width="60" height="30" class="gray-light interactive"/>
            <text x="180" y="400" class="tech-item">Node.js</text>

            <rect x="80" y="420" width="60" height="30" class="gray-light interactive"/>
            <text x="110" y="440" class="tech-item">npm</text>

            <rect x="150" y="420" width="60" height="30" class="gray-light interactive"/>
            <text x="180" y="440" class="tech-item">yarn</text>

            <!-- Build Tools -->
            <rect x="220" y="340" width="80" height="120" class="red-section"/>
            <text x="260" y="365" class="section-title">构</text>
            <text x="260" y="385" class="section-title">建</text>
            <text x="260" y="405" class="section-title">工</text>
            <text x="260" y="425" class="section-title">具</text>

            <!-- Testing Tools -->
            <rect x="310" y="340" width="80" height="30" class="red-section"/>
            <text x="350" y="360" class="section-title">测试工具</text>

            <rect x="310" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="350" y="397" class="tech-item">JUnit</text>

            <rect x="310" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="350" y="427" class="tech-item">Mockito</text>

            <rect x="310" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="350" y="457" class="tech-item">TestNG</text>

            <!-- Monitoring Tools -->
            <rect x="400" y="340" width="80" height="30" class="red-section"/>
            <text x="440" y="360" class="section-title">监控工具</text>

            <rect x="400" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="440" y="397" class="tech-item">Skywalking</text>

            <rect x="400" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="440" y="427" class="tech-item">Prometheus</text>

            <rect x="400" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="440" y="457" class="tech-item">Grafana</text>

            <!-- Container Technology -->
            <rect x="490" y="340" width="80" height="30" class="red-section"/>
            <text x="530" y="360" class="section-title">容器技术</text>

            <rect x="490" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="530" y="397" class="tech-item">Docker</text>

            <rect x="490" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="530" y="427" class="tech-item">Kubernetes</text>

            <rect x="490" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="530" y="457" class="tech-item">Helm</text>

            <!-- DevOps Tools -->
            <rect x="580" y="340" width="80" height="30" class="red-section"/>
            <text x="620" y="360" class="section-title">DevOps工具</text>

            <rect x="580" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="620" y="397" class="tech-item">Jenkins</text>

            <rect x="580" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="620" y="427" class="tech-item">GitLab CI</text>

            <rect x="580" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="620" y="457" class="tech-item">Ansible</text>

            <!-- Database Technologies -->
            <rect x="670" y="340" width="80" height="30" class="red-section"/>
            <text x="710" y="360" class="section-title">数据库技术</text>

            <rect x="670" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="710" y="397" class="tech-item">MySQL</text>

            <rect x="670" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="710" y="427" class="tech-item">Redis</text>

            <rect x="670" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="710" y="457" class="tech-item">MongoDB</text>

            <!-- Security Components -->
            <rect x="760" y="340" width="80" height="30" class="red-section"/>
            <text x="800" y="360" class="section-title">安全组件</text>

            <rect x="760" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="800" y="397" class="tech-item">OAuth2</text>

            <rect x="760" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="800" y="427" class="tech-item">JWT</text>

            <rect x="760" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="800" y="457" class="tech-item">Spring Security</text>

            <!-- API Gateway -->
            <rect x="850" y="340" width="80" height="30" class="red-section"/>
            <text x="890" y="360" class="section-title">API网关</text>

            <rect x="850" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="890" y="397" class="tech-item">Zuul</text>

            <rect x="850" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="890" y="427" class="tech-item">Gateway</text>

            <rect x="850" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="890" y="457" class="tech-item">Kong</text>

            <!-- Search Engine -->
            <rect x="940" y="340" width="80" height="30" class="red-section"/>
            <text x="980" y="360" class="section-title">搜索引擎</text>

            <rect x="940" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="980" y="397" class="tech-item">Elasticsearch</text>

            <rect x="940" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="980" y="427" class="tech-item">Solr</text>

            <rect x="940" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="980" y="457" class="tech-item">Lucene</text>

            <!-- Big Data -->
            <rect x="1030" y="340" width="80" height="30" class="red-section"/>
            <text x="1070" y="360" class="section-title">大数据</text>

            <rect x="1030" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="1070" y="397" class="tech-item">Hadoop</text>

            <rect x="1030" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="1070" y="427" class="tech-item">Spark</text>

            <rect x="1030" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="1070" y="457" class="tech-item">Kafka</text>

            <!-- Cloud Native -->
            <rect x="1120" y="340" width="80" height="30" class="red-section"/>
            <text x="1160" y="360" class="section-title">云原生</text>

            <rect x="1120" y="380" width="80" height="25" class="white-box interactive"/>
            <text x="1160" y="397" class="tech-item">Istio</text>

            <rect x="1120" y="410" width="80" height="25" class="white-box interactive"/>
            <text x="1160" y="427" class="tech-item">Envoy</text>

            <rect x="1120" y="440" width="80" height="25" class="white-box interactive"/>
            <text x="1160" y="457" class="tech-item">Consul</text>

            <!-- Infrastructure Layer -->
            <rect x="0" y="480" width="1600" height="40" class="red-dark"/>
            <text x="800" y="505" class="section-title" font-size="16">基础设施层</text>

            <!-- Infrastructure Components -->
            <rect x="50" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="110" y="550" class="tech-item-white">服务器集群</text>
            <text x="110" y="570" class="tech-item-white">负载均衡</text>

            <rect x="180" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="240" y="550" class="tech-item-white">网络设备</text>
            <text x="240" y="570" class="tech-item-white">防火墙</text>

            <rect x="310" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="370" y="550" class="tech-item-white">存储系统</text>
            <text x="370" y="570" class="tech-item-white">备份系统</text>

            <rect x="440" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="500" y="550" class="tech-item-white">监控系统</text>
            <text x="500" y="570" class="tech-item-white">日志系统</text>

            <rect x="570" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="630" y="550" class="tech-item-white">CI/CD平台</text>
            <text x="630" y="570" class="tech-item-white">自动化部署</text>

            <rect x="700" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="760" y="550" class="tech-item-white">容器编排</text>
            <text x="760" y="570" class="tech-item-white">服务网格</text>

            <rect x="830" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="890" y="550" class="tech-item-white">安全防护</text>
            <text x="890" y="570" class="tech-item-white">访问控制</text>

            <rect x="960" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="1020" y="550" class="tech-item-white">数据中心</text>
            <text x="1020" y="570" class="tech-item-white">云平台</text>

            <rect x="1090" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="1150" y="550" class="tech-item-white">运维工具</text>
            <text x="1150" y="570" class="tech-item-white">性能调优</text>

            <rect x="1220" y="530" width="120" height="60" class="red-category interactive"/>
            <text x="1280" y="550" class="tech-item-white">灾备系统</text>
            <text x="1280" y="570" class="tech-item-white">高可用</text>

            <!-- Footer -->
            <rect x="0" y="610" width="1600" height="50" class="red-header"/>
            <text x="800" y="640" class="section-title" font-size="14">产品统一开发底座技术路线图 - 支撑企业级应用开发的完整技术栈</text>

        </svg>
    </div>
</body>
</html>
